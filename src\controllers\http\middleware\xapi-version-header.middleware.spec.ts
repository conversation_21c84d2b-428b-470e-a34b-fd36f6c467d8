import { expect } from 'chai'
import httpStatus from 'http-status'
const { BAD_REQUEST, OK } = httpStatus
import httpMocks from 'node-mocks-http'
import Sinon from 'sinon'
import middleware from './xapi-version-header.middleware.js'
import settings from '../../../config/settings.js'

describe('HTTP Middleware: XAPI Header', () => {
  afterEach(() => Sinon.restore())

  it('should reject requests with missing "X-Experience-API-Version" header.', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks()
    middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.false
    const temp = mocks.res._getData()
    console.log(temp)
    expect(mocks.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks.res._getData()).to.eq('X-Experience-API-Version header missing')
    expect(mocks.res.statusCode).to.eq(BAD_REQUEST)
  })

  it('should include the "X-Experience-API-Version" header in every response.', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks({ headers: { 'X-Experience-API-Version': '2.0.0' } })
    middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.true
    expect(mocks.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks.res.statusCode).to.eq(OK)
  })

  it('should reject url encoded form requests since alternate request syntax is not supported', () => {
    const next1 = Sinon.spy()
    const mocks1 = httpMocks.createMocks({
      headers: {
        "content-type": 'application/x-www-form-urlencoded',
        'X-Experience-API-Version': '2.0.0'
      },
      body: { 'X-Experience-API-Version': '2.0.0' }
    })
    middleware(mocks1.req, mocks1.res, next1)
    expect(next1.called).to.be.false
    expect(mocks1.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks1.res._getData()).to.eq('Alternate request syntax is not supported in xAPI 2.0')
    expect(mocks1.res.statusCode).to.eq(BAD_REQUEST)
  })

  it('should accept requests with xAPI 2.0.0 version header', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks({ headers: { 'X-Experience-API-Version': '2.0.0' } })
    middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.true
    expect(mocks.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks.res.statusCode).to.eq(OK)
  })

  it('should reject requests with version header other than 2.0.0', () => {
    const next1 = Sinon.spy()
    const mocks1 = httpMocks.createMocks({ headers: { 'X-Experience-API-Version': '1.0.3' } })
    middleware(mocks1.req, mocks1.res, next1)
    expect(next1.called).to.be.false
    expect(mocks1.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks1.res._getData()).to.eq('X-Experience-API-Version is not supported')
    expect(mocks1.res.statusCode).to.eq(BAD_REQUEST)

    const next2 = Sinon.spy()
    const mocks2 = httpMocks.createMocks({ headers: { 'X-Experience-API-Version': '1.1.0' } })
    middleware(mocks2.req, mocks2.res, next2)
    expect(next2.called).to.be.false
    expect(mocks2.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks2.res._getData()).to.eq('X-Experience-API-Version is not supported')
    expect(mocks2.res.statusCode).to.eq(BAD_REQUEST)
  })

  it('should reject alternate request syntax since only xAPI 2.0 is supported', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks({
      headers: {
        'content-type': 'application/x-www-form-urlencoded',
        'X-Experience-API-Version': '2.0.0'
      },
      body: { 'X-Experience-API-Version': '2.0.0' }
    })
    middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.false
    expect(mocks.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks.res._getData()).to.eq('Alternate request syntax is not supported in xAPI 2.0')
    expect(mocks.res.statusCode).to.eq(BAD_REQUEST)
  })
})
