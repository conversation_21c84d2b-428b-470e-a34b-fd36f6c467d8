import { expect } from 'chai'
import httpStatus from 'http-status'
import httpMocks from 'node-mocks-http'
import get from './get-about.controller.js'
import logger from '@lcs/logger'

describe('HTTP about controller', () => {
  before(async () => {
    await logger.init({ level: 'silly' })
  })

  it('Successfully retrieved about endpoint', async () => {
    const mocks = httpMocks.createMocks({})
    await get(mocks.req, mocks.res)
    expect(mocks.res.statusCode).equal(httpStatus.OK)
    const response = JSON.parse(mocks.res._getData())
    expect(response).to.exist
    expect(response.version).to.exist
    expect(Array.isArray(response.version)).equal(true)
    expect(response.version.length).to.be.gte(1)
    // check if version property is a string array
    response.version.forEach((element: any) => {
      expect(typeof element).equal('string')
    });
    expect(response.version).to.contain('2.0.0')
  })

})