import { Request } from 'express'

/**
 * Extracts the xAPI version from the request headers or form body
 * @param req Express request object
 * @returns The xAPI version string or undefined if not found
 */
export function getXApiVersionFromRequest(req: Request): string | undefined {
  let version = req.get('X-Experience-API-Version') ?? req.get('HTTP_X_EXPERIENCE_API_VERSION') ?? req.get('X_Experience_API_Version')

  // check for content type 'application/x-www-form-urlencoded'
  if (req.get('content-type')?.includes('application/x-www-form-urlencoded') && req.body) {
    version = req.body['X-Experience-API-Version'] ?? req.body['X_Experience_API_Version'] ?? version
  }

  return version
}

/**
 * Checks if alternate request syntax (application/x-www-form-urlencoded) should be allowed
 * based on the xAPI version. Since we only support xAPI 2.0, alternate request syntax is never allowed.
 * @param req Express request object
 * @returns false - alternate request syntax is never allowed in xAPI 2.0
 */
export function shouldAllowAlternateRequestSyntax(req: Request): boolean {
  // We only support xAPI 2.0, which does not support alternate request syntax
  return false
}

/**
 * Checks if the request is using alternate request syntax (application/x-www-form-urlencoded)
 * @param req Express request object
 * @returns true if using alternate request syntax, false otherwise
 */
export function isUsingAlternateRequestSyntax(req: Request): boolean {
  return req.get('content-type')?.includes('application/x-www-form-urlencoded') === true
}
