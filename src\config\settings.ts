import <PERSON><PERSON><PERSON> from 'prettyjson'
import { readFileSync } from 'fs'
import tessConfigLoader from '@tess-f/shared-config'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename)

const configPaths = process.env.CONFIG_PATHS ? process.env.CONFIG_PATHS.split(',') : []
const tessConfig = tessConfigLoader.load(configPaths)
const xAPIVersion = '2.0.0'
console.log('TESS shared config loaded\t' + configPaths)

export default {
  apiBaseRoute: process.env.API_BASE_ROUTE || '/',
  languageCode: process.env.LANGUAGE_CODE || 'en-US',
  XAPI_VERSION: xAPIVersion,
  XAPI_VERSIONS: ['1.0.3', xAPIVersion],
  server: {
    version: '0.0.1',
    log_all_requests: process.env.LOG_ALL_REQUESTS === 'true',
    timeout: process.env.SERVER_TIMEOUT ? parseInt(process.env.SERVER_TIMEOUT) : 240000,
    port: process.env.PORT || 8080,
    address: process.env.SERVER_ADDRESS || 'localhost'
  },
  logger: {
    level: tessConfig.logger.level,
    name: process.env.LOG_NAME || 'learning-record-store',
    useConsole: tessConfig.logger.useConsole,
    useElasticsearch: tessConfig.logger.useElasticsearch,
    noColor: tessConfig.logger.noColor,
    elasticsearch: {
      index: process.env.ELASTICSEARCH_INDEX || 'lrs',
      options: {
        node: tessConfig.elasticsearch.host,
        auth: tessConfig.elasticsearch.elasticPassword
          ? {
            username: tessConfig.elasticsearch.elasticUsername,
            password: tessConfig.elasticsearch.elasticPassword
          }
          : undefined
      }
    }
  },
  amqp: {
    rpc_timeout: tessConfig.rabbitmq.remoteProcedureCalls.timeoutMilli,
    rpcQueue: tessConfig.rabbitmq.remoteProcedureCalls.learningRecordStore.queue,
    config: {
      protocol: tessConfig.rabbitmq.protocol,
      hostname: tessConfig.rabbitmq.host,
      port: tessConfig.rabbitmq.port,
      username: tessConfig.rabbitmq.username,
      password: tessConfig.rabbitmq.password
    },

    statementExchange: {
      name: tessConfig.rabbitmq.exchanges.learningRecordStore.name,
      routes: tessConfig.rabbitmq.exchanges.learningRecordStore.routes
    },

    serviceQueues: {
      fds: tessConfig.rabbitmq.remoteProcedureCalls.fileDelivery.queue,
      identityManagement: tessConfig.rabbitmq.remoteProcedureCalls.identityManagement.queue
    }
  },

  redis: {
    url: tessConfig.redis.host,
    password: tessConfig.redis.password,
    verbDB: tessConfig.redis.databases.lrsVerb,
    verbDisplayDB: tessConfig.redis.databases.lrsVerbDisplay
  },

  sessionAuthority: {
    bypass: process.env.SESSION_AUTHORITY_BYPASS === 'true',
    redisUrl: tessConfig.redis.host,
    redisPassword: tessConfig.redis.password,
    redisSessionDatabase: tessConfig.redis.databases.sessions,
    redisSessonLookupDatabase: tessConfig.redis.databases.sessionLookup,
    jwtSecret: tessConfig.security.jwtSecret,
    cookieSecret: tessConfig.security.cookieSecret,
    cookieSecure: tessConfig.security.cookieSecure,
    cookieSigned: tessConfig.security.cookieSigned,
    options: {
      hmacSha256Path: tessConfig.security.hmacSha256Path,
      jwtDefaultExpiresIn: tessConfig.security.session.expiresInMilli
    }
  },

  mssql: {
    forceEncrypted: tessConfig.microsoftSql.forceEncrypted,
    streamChunkSize: tessConfig.microsoftSql.streamChunkSize,
    connectionConfig: {
      user: tessConfig.microsoftSql.username,
      password: tessConfig.microsoftSql.password,
      server: tessConfig.microsoftSql.host,
      database: tessConfig.microsoftSql.database,
      port: tessConfig.microsoftSql.port,
      debug: tessConfig.microsoftSql.debug,
      requestTimeout: tessConfig.microsoftSql.requestTimeout,
      connectionTimeout: tessConfig.microsoftSql.connectionTimeout,
      options: {
        encrypt: tessConfig.microsoftSql.encrypt,
        trustedConnection: tessConfig.microsoftSql.trustedConnection,
        enableArithAbort: true,
        trustServerCertificate: tessConfig.microsoftSql.trustServerCertificate
      },
      pool: {
        max: tessConfig.microsoftSql.maxPoolSize,
        min: tessConfig.microsoftSql.minPoolSize,
        idleTimeoutMillis: tessConfig.microsoftSql.idleTimeoutMillisPool
      }
    }
  },

  print: function () {
    const _sanitizedCopy = JSON.parse(JSON.stringify(this))

    if (this.logger.elasticsearch.options.auth) {
      _sanitizedCopy.logger.elasticsearch.options.auth.password = '<hidden>'
    }
    _sanitizedCopy.mssql.connectionConfig.password = '<hidden>'
    _sanitizedCopy.sessionAuthority.jwtSecret = '<hidden>'
    _sanitizedCopy.sessionAuthority.cookieSecret = '<hidden>'
    _sanitizedCopy.redis.password = '<hidden>'
    _sanitizedCopy.sessionAuthority.redisPassword = '<hidden>'
    _sanitizedCopy.amqp.config.password = '<hidden>'

    const logo = readFileSync(path.join(__dirname, '/logo.txt')).toString()

    console.log(logo)
    console.log('--------------------')
    console.log(prettyjson.render(_sanitizedCopy, { noColor: this.logger.noColor }))
    console.log('--------------------')
  }
}
